<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="7a70bde0-52be-4a3c-ae02-f0a88d4811e5" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/gui-xhs-slide-list.vue" beforeDir="false" afterPath="$PROJECT_DIR$/gui-xhs-slide-list.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../../pages/switchPages/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../../pages/switchPages/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/../.." />
  </component>
  <component name="ProjectId" id="2rs4ktuOcTUjXbD99Rq8yDeO64z" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="E:/2025Project/github/xiaohongshu" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="SshConsoleOptionsProvider">
    <option name="myEncoding" value="UTF-8" />
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7a70bde0-52be-4a3c-ae02-f0a88d4811e5" name="Default Changelist" comment="" />
      <created>1737334594704</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1737334594704</updated>
      <workItem from="1737334596127" duration="675000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="2" />
  </component>
  <component name="WindowStateProjectService">
    <state x="687" y="214" key="FileChooserDialogImpl" timestamp="1737334648315">
      <screen x="0" y="0" width="1920" height="1040" />
    </state>
    <state x="687" y="214" key="FileChooserDialogImpl/0.0.1920.1040@0.0.1920.1040" timestamp="1737334648315" />
  </component>
</project>