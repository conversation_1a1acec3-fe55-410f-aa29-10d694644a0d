<template>
	<scroll-view
			class="gui-slide-list gui-absolute-full"
			:scroll-y="true"
			:show-scrollbar="false"
			:style="{width:width+'rpx'}"
			@scrolltolower="scrolltolower"
	>
		<view style="height:330rpx;">
			<view class="gui-flex gui-padding">
				<view style="flex: 1;text-align:right"></view>
				<view class="xhs-font35">消息</view>
				<view class="xhs-font30" style="flex: 1;text-align:right">
					<text class=" gui-icons ">&#xe611;</text> 发现群聊
				</view>
			</view>
			<view class="gui-grid gui-space-between " style="padding:50rpx 60rpx;">
				<view class="gui-grid-item gui-relative " @tap="goReceiveUp">
					<view
							class=" gui-flex gui-justify-content-center gui-align-items-center"
							style="width: 100rpx;height: 100rpx;border-radius: 35rpx;background:#FFECEB; "
					>
						<text
								class="gui-grid-icon gui-icons "
								style="color: #FA5F5A;font-size: 50rpx;"
						>&#xe605;</text>
					</view>
					<text class="gui-grid-text gui-icons xhs-margin-top-10 xhs-font26 ">赞和收藏</text>
					<view
							v-show="unReadData.upNotesUnReadCount>0||unReadData.starNotesUnReadCount>0"
							class="gui-bg-red gui-color-white top-unRead"
					>
						{{unReadData.upNotesUnReadCount + unReadData.starNotesUnReadCount}}
					</view>
				</view>
				<view class="gui-grid-item gui-space-between gui-relative" @tap="goNewFollow">
					<view
							class=" gui-flex gui-justify-content-center gui-align-items-center"
							style="width: 100rpx;height: 100rpx;border-radius: 35rpx;background:#E8EFFF; "
					>
						<text
								class="gui-grid-icon gui-icons"
								style="color: #3786FD;font-size: 50rpx;"
						>&#xe6fe;</text>
					</view>
					<text class="gui-grid-text gui-icons xhs-margin-top-10 xhs-font26 ">新增关注</text>
					<view
							v-show="unReadData.followUnReadCount>0"
							class="gui-bg-red gui-color-white top-unRead"
					>{{unReadData.followUnReadCount}}</view>
				</view>
				<view class="gui-grid-item gui-space-between gui-relative"  @tap="goReplyMe">
					<view
							class=" gui-flex gui-justify-content-center gui-align-items-center"
							style="width: 100rpx;height: 100rpx;border-radius: 35rpx;background:#E5FAF1; "
					>
						<text
								class="gui-grid-icon gui-icons"
								style="color: #34DA9A;font-size: 50rpx;"
						>&#xe6b8;</text>
					</view>
					<text class="gui-grid-text gui-icons xhs-margin-top-10 xhs-font26 ">评论和@</text>
					<view
							v-show="unReadData.replyUnReadCount>0"
							class="gui-bg-red gui-color-white top-unRead"
					>{{unReadData.replyUnReadCount}}</view>
				</view>
			</view>
		</view>
		<view v-if="showPromotion">
			<gui-speaker
					style="margin-left: 30rpx;"
					:items="speakerMsgs"
			>
				<template v-slot:icon>
					<text
							class=" gui-icons demo-icon gui-primary-color"
					>&#xe656;</text>
				</template>
			</gui-speaker>
		</view>
		<view
				@tap.stop.prevent="goNotice"
				class="gui-slide-list-item xhs-margin-top-15 "
				:style="{width:width+'rpx'}"
		>
			<view
					class="gui-flex gui-row gui-nowrap gui-align-items-center"
					:style="{
                    width:(width + btnWidth)+'rpx',
                    overflow:'hidden'
                }"
			>
				<view
						style="width: 100rpx;height: 100rpx;border-radius: 100rpx;"
						class="gui-slide-list-img-wrap"
						hover-class="gui-tap"
				>
					<image
							src="http://*************:8080/upload/avatar/notice.png"
							style="width: 100rpx;height: 100rpx;border-radius: 100rpx;"
							class="gui-slide-list-img"
							mode="aspectFill"
					></image>
				</view>
				<view
						class="gui-slide-list-content"
						hover-class="gui-tap"
				>
					<view
							class="gui-flex gui-row  gui-nowrap gui-space-between gui-relative"
					>
						<text
								:class="[titleEllipsis?'gui-ellipsis':'']"
								class="gui-slide-list-title-text xhs-font30 gui-block xhs-letter-spacing1 "
								style="margin-top: -6rpx;"
						>系统消息</text>
						<text
								class="gui-slide-list-desc gui-block gui-color-gray ps-space gui-flex-shrink0"
						>{{receiveLast && receiveLast.createTime? formatDate(receiveLast.createTime) : ''}}</text>
						<text
								style="position: absolute;right: 20rpx; top:40rpx;border-radius:28rpx; height:28rpx; line-height:28rpx; padding:0 13rpx; font-size:22rpx;"
								class="gui-bg-red gui-color-white "
								v-if="unReadData.receiveUnReadCount > 0"
						>{{unReadData.receiveUnReadCount}}</text>
					</view>
					<text
							:class="[descEllipsis?'gui-ellipsis':'']"
							class="gui-slide-list-desc gui-third-text gui-block xhs-font26 xhs-letter-spacing1"
					>{{receiveLast && receiveLast.noticeTitle || ''}}</text>
				</view>
				<view
						class="gui-slide-btns gui-flex gui-row gui-nowrap"
						:style="{width:(btnWidth-2) +'rpx'}"
				></view>
			</view>
		</view>
		<view
				class="gui-slide-list-item xhs-margin-top-15  "
				v-for="(item, index) in msgsIn"
				:key="index"
				:style="{width:width+'rpx'}"
		>
			<view
					class="gui-flex gui-row gui-nowrap gui-align-items-center"
					:style="{
                    width:(width + btnWidth)+'rpx',
                    overflow:'hidden',
                    transform:'translateX('+(moveIndex != index ? 0 : x)+'px)'
                }"
			>
				<view
						style="width: 100rpx;height: 100rpx;border-radius: 100rpx;"
						class="gui-slide-list-img-wrap"
						hover-class="gui-tap"
						@tap.stop.prevent="itemTap(index)"
						@longpress.stop.prevent="itemLongTap(index)"
				>
					<image
							lazy-load
							@tap.stop.prevent="goCard(item && item.authorId)"
							style="width: 100rpx;height: 100rpx;border-radius: 100rpx;"
							:src="item && item.authorAvatar"
							class="gui-slide-list-img"
							mode="aspectFill"
					></image>
				</view>
				<view
						@tap.stop.prevent="itemTap(index)"
						class="gui-slide-list-content"
						hover-class="gui-tap"
				>
					<view
							class="gui-flex gui-row  gui-nowrap gui-space-between gui-relative"
					>
						<text
								:class="[titleEllipsis?'gui-ellipsis':'']"
								class="gui-slide-list-title-text xhs-font30 gui-block xhs-letter-spacing1 "
								style="margin-top: -6rpx;"
						>{{item && item.authorName || ''}}</text>
						<text
								class="gui-slide-list-desc gui-block gui-color-gray ps-space gui-flex-shrink0"
						>{{item && item.createTime? messageFormatDate(item.createTime) : ''}}</text>
						<text
								style="position: absolute;right: 20rpx; top:40rpx;border-radius:28rpx; height:28rpx; line-height:28rpx; padding:0 13rpx; font-size:22rpx;"
								class="gui-bg-red gui-color-white "
								v-if="item && item.unReadCount > 0"
						>{{item.unReadCount}}</text>
					</view>
					<text
							:class="[descEllipsis?'gui-ellipsis':'']"
							class="gui-slide-list-desc gui-third-text gui-block xhs-font26 xhs-letter-spacing1"
					>
						<!-- #ifdef H5 -->
						<rich-text :nodes="parserHtml(item.content)"></rich-text>
						<!-- #endif -->
						<!-- #ifdef APP-PLUS -->
						<rich-text :nodes="parserHtml(item.content)"></rich-text>
						<!-- #endif -->
						<!-- #ifdef MP -->
						{{item && item.content || ''}}
						<!-- #endif -->

					</text>
				</view>
				<view
						class="gui-slide-btns gui-flex gui-row gui-nowrap"
						:style="{width:(btnWidth-2) +'rpx'}"
				></view>
			</view>
		</view>
		<!-- 加载组件 -->
		<view style="padding:20rpx;">
			<gui-loadmore
					ref="loadmoreinslidelist"
			></gui-loadmore>
		</view>
	</scroll-view>
	<gui-action-sheet
			height=180
			ref="guiactionsheet"
			@selected="selected"
			:items="actionSheetItems"
	></gui-action-sheet>
</template>
<script>
	import parserHtml from "@/Grace6/js/parserHTML.js";
	import graceRequestConfig from '@/custom/graceRequestConfig.js';

	export default {
		name: "gui-slide-list",
		props: {
			receiveLast: {
				type: Object,
				default: function () {
					return {
						createTime: '',
						noticeTitle: ''
					};
				}
			},
			unReadData: { type: Object, default: function () { return {}; } },
			width: { type: Number, default: 750 },
			msgs: { type: Array, default: function () { return []; } },
			btnWidth: { type: Number, default: 320 },
			titleEllipsis: { type: Boolean, default: true },
			descEllipsis: { type: Boolean, default: true }
		},
		data() {
			return {
				showPromotion: false,
				speakerMsgs: [
					{ title: "商务　微信: dadait24　　qq: 727869402", }
				],
				actionSheetItems: ['置顶聊天', '删除'],
				msgsIn: [
					{ createTime: '' }
				],
				damping: 0.29,
				moveIndex: -1,
				x: 0,
				oX: 0,
				scY: true,
				btnWidthpx: 160,
				touchStart: false
			};
		},
		created: function () {
			this.showPromotion = graceRequestConfig.showPromotion;
			this.init(this.msgs);
			this.btnWidthpx = (uni.upx2px(this.btnWidth) * -1) + 2;
		},
		watch: {
			msgs: function (nv) {
				this.init(nv);
			}
		},
		methods: {
			parserHtml(data){
				return parserHtml.parserHTML(data);
			},
			goReceiveUp() {
				uni.app.navigate('/pages/receiveUp/receiveUp');
			},
			goNewFollow(authorId) {
				uni.app.navigate('/pages/newFollow/newFollow');
			},
			goReplyMe(authorId) {
				uni.app.navigate('/pages/replays/replays');
			},
			goNotice() {
				uni.app.navigate('/pages/notice/notice');
			},
			goCard(authorId) {
				return uni.app.goCard(authorId);
			},
			formatDate(date) {
				return uni.app.formatDate(date);
			},
			messageFormatDate(date) {
				return uni.app.messageFormatDate(date);
			},
			selected(index) {
				console.log(index);
				// 返回索引，可以根据索引获取文本数据
			},
			init(msgs) {
				this.moveIndex = -1;
				this.msgsIn = msgs;
			},
			thStart(e, index) {
				this.x = 0;
				this.moveIndex = index[0];
				this.damping = 0.25;
			},
			thMove(e, index) {
				var x = e[0][0];
				var y = e[0][1];
				if (Math.abs(x) < Math.abs(y)) {
					this.scY = true;
					return;
				} else {
					this.scY = false;
				}
				if (x < 0) {
					this.x += x * this.damping;
					if (this.x < this.btnWidthpx) {
						this.x = this.btnWidthpx;
					}
					this.damping *= 1.02;
				} else {
					this.scY = true;
				}
			},
			thEnd(e, index) {
				if (this.x > this.btnWidthpx / 8) {
					this.x = 0;
				} else {
					this.x = this.btnWidthpx;
				}
				this.scY = true;
				this.oX = this.x;
			},
			btnTap(index, indexBtn) {
				this.$emit('btnTap', index, indexBtn);
			},
			itemLongTap() {
				console.log('我被点击了');
				this.$refs.guiactionsheet.open();
			},
			itemTap(index) {
				if (this.oX < 0) {
					this.oX = 0;
					this.moveIndex = -1;
					return;
				}
				this.$emit('itemTap', index);
				this.moveIndex = -1;
				this.oX = 0;
			},
			scrolltolower() {
				var laodStatus = this.$refs.loadmoreinslidelist.loadMoreStatus;
				if (laodStatus === 0) {
					this.$emit('scrolltolower');
				}
			},
			startLoadig() {
				this.$refs.loadmoreinslidelist.loading();
			},
			nomore() {
				this.$refs.loadmoreinslidelist.nomore();
			},
			endLoading() {
				this.$refs.loadmoreinslidelist.stoploadmore();
			}
		},
		emits: ['btnTap', 'scrolltolower']
	};
</script>
<style scoped>
	.ps-space {
		padding-left: 20rpx;
	}
	.top-unRead {
		border: 4rpx solid #FFFFFF;
		position: absolute;
		right: 5rpx;
		top: -11rpx;
		border-radius: 35rpx;
		height: 35rpx;
		width: 35rpx;
		line-height: 35rpx;
		text-align: center;
		font-size: 22rpx;
	}
</style>
