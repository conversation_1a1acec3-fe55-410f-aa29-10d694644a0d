<template>

		<view
				v-show="user.authorId!=author.authorId&&author.isFollow&&author.isFollowMe"
				@tap="cancelfollowAuthor(author)"
				class="gui-button-mini  xhs-border-radius50 xhs-border-gray follow-button"
				>
			<text class="gui-icons xhs-font24 ">互相关注</text>
		</view>

		<view
				v-show="user.authorId!=author.authorId&&!author.isFollow&&author.isFollowMe"
				@tap="followAuthor(author)"
				type="default"
				class="gui-button-mini xhs-border-radius50 xhs-border-pink follow-button"
				>
			<text style="color: #E22E4D;" class=" gui-icons xhs-font24">回关</text>
		</view>


		<view
				v-show="user.authorId!=author.authorId&&!author.isFollow&&!author.isFollowMe"
				@tap="followAuthor(author)"
				type="default"
				class="gui-button-mini xhs-border-radius50 xhs-border-pink follow-button"
				>
			<text style="color: #E22E4D;" class="gui-icons xhs-font24">关注</text>
		</view>

		<view
				v-show="user.authorId!=author.authorId&&author.isFollow&&!author.isFollowMe"
				@tap="cancelfollowAuthor(author)"
				type="default"
				class="gui-button-mini xhs-border-radius50 xhs-border-gray follow-button "
				>
			<text class=" gui-icons xhs-font24">已关注</text>
		</view>

</template>
<script>
export default {
	name  : "gui-xhs-follow",
	props : {
		author      : {
			type     : Object,
			default  : function(){return new Object();}
		},
	},
	data(){
		return {
			user: uni.app.getStorage('user')

		}
	},
	created:function(){

	},
	watch:{

	},
	methods:{
		cancelfollowAuthor(author){
			uni.app.post('/auth/cancelfollowAuthor',{authorId:author.authorId},'json',"",(res=>{
				this.$emit('cancelfollowAuthor', author);

			}))
		},
		followAuthor(author){
			uni.app.post('/auth/followAuthor',{authorId:author.authorId},'json',"",(res=>{
				this.$emit('followAuthor', author);
			}))
		},
	}
}
</script>
<style scoped>
.follow-button{
	width:150rpx;margin-right: 20rpx;text-align: center;
}
</style>
