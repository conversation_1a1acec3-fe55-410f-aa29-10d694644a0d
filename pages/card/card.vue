<template>
  <gui-page :customHeader="true" class="gui-bg-white" :isHeaderSized="false">
    <template v-slot:gHeader>
      <view
        :class="[isFixed ? 'gui-bg-white' : '']"
        style="height: 88rpx"
        class="gui-flex gui-nowrap gui-rows gui-align-items-center"
      >
        <!-- 使用组件实现返回按钮及返回首页按钮 -->
        <gui-xhs-header-leading
          :backButtonClass="[isFixed ? '' : 'gui-color-black']"
          :home="false"
          @goBack="goBack"
        ></gui-xhs-header-leading>
        <!-- 导航文本此处也可以是其他自定义内容 -->
        <view
          v-show="isFixed"
          class="gui-flex1 align-items-center gui-justify-content-center gui-nowrap gui-flex gug-row"
          style="margin-left: 20rpx"
        >
          <image
            @tap="goCard(author.authorId)"
            style="width: 70rpx; height: 70rpx; border-radius: 70rpx"
            :src="author.avatarUrl"
            class="gui-slide-list-img xhs-margin-right-20 animate slideInUp"
            mode="aspectFill"
          ></image>
        </view>
        <!-- 此处加一个右侧展位元素与左侧同宽，实现标题居中 -->
        <!-- 实际宽度请根据自己情况设置 -->

        <!-- 如果右侧有其他内容可以利用条件编译和定位来实现-->
      </view>
    </template>

    <template v-slot:gBody>
      <!-- #ifdef MP -->
      <view class="status_bar">
        <!-- 这里是状态栏 -->
      </view>
      <view class="status_bar">
        <!-- 这里是状态栏 -->
      </view>
      <view class="status_bar">
        <!-- 这里是状态栏 -->
      </view>
      <!-- #endif -->

      <view style="background-color: #f8f8f8">
        <view
          v-if="backgroundUrl.length > 0"
          :style="{
            'background-image': `url(${formatImgUrl(author.backGroundPic)})`,
            'background-size': 'cover',
            'background-position': 'center',
            'background-repeat': 'no-repeat',
          }"
        >
          <view class="bg-element">
            <view class="gui-list gui-padding">
              <view class="gui-list-items">
                <view
                  style="width: 150rpx; height: 150rpx"
                  class="gui-list-image ucenter-face"
                >
                  <image
                    style="
                      width: 150rpx;
                      height: 150rpx;
                      border: 1rpx solid #ffffff;
                    "
                    class="gui-list-image ucenter-face-image"
                    :src="author.userPic"
                    mode="aspectFill"
                  ></image>
                </view>
                <view class="gui-list-body">
                  <view class="gui-list-title">
                    <text
                      style="font-weight: bold; font-size: 35rpx"
                      class="gui-list-title-text gui-color-white"
                    >
                      {{ author.nickname }}
                    </text>
                  </view>
                  <text
                    class="gui-list-body-desc gui-color-white xhs-margin-top-10 xhs-font20"
                    >摇爪号:&nbsp;&nbsp;{{ author.authorNo }} &nbsp;<text
                      class="gui-icons"
                      >&#xe631;</text
                    ></text
                  >
                  <text
                    class="gui-list-body-desc gui-color-white xhs-margin-top-5 xhs-font20"
                    >IP:&nbsp;&nbsp;{{ author.ipRealAddress }} &nbsp;<text
                      class="gui-icons"
                      >&#xe6a1;</text
                    ></text
                  >
                </view>
              </view>
              <view class="gui-color-white gui-margin-top">
                {{ author.description }}
              </view>
              <view class="gui-color-white gui-margin-top gui-flex xhs-font20">
                <text class="author-tag gui xhs-maoboli">
                  <text
                    v-if="author.age"
                    style="color: #90bae0"
                    class="gui-icons"
                    >&#xe618;</text
                  >
                  {{ author.age }}岁
                </text>
                <text
                  v-if="author.area"
                  class="author-tag xhs-maoboli xhs-font20"
                >
                  {{ author.area }}
                </text>
              </view>

              <view class="gui-flex gui-space-between">
                <view
                  class="gui-flex gui-color-white gui-margin-top xhs-font30"
                >
                  <view style="width: 100rpx; text-align: center">
                    <view>{{ author.follow }}</view>
                    <view class="xhs-font24 xhs-margin-top-10">关注</view>
                  </view>
                  <view style="width: 100rpx; text-align: center">
                    <view>{{ author.fans }}</view>
                    <view class="xhs-font24 xhs-margin-top-10">粉丝</view>
                  </view>
                  <view style="width: 160rpx; text-align: center">
                    <view>{{ author.upAndStarCount }}</view>
                    <view class="xhs-font24 xhs-margin-top-10">获赞与收藏</view>
                  </view>
                </view>

                <view
                  class="gui-flex gui-align-items-center gui-justify-content-center"
                >
                  <button
                    v-show="author.isFollow"
                    @tap="cancelfollowAuthor"
                    type="default"
                    class="gui-button-mini xhs-border-radius50 xhs-border-white"
                    style="
                      width: 150rpx;
                      margin-right: 20rpx;
                      background: transparent;
                      backdrop-filter: blur(10px);
                      background-color: rgba(255, 255, 255, 0.1);
                    "
                  >
                    <text class="gui-color-white gui-icons">取消关注</text>
                  </button>

                  <button
                    v-show="!author.isFollow && author.isFollowMe"
                    @tap="followAuthor"
                    type="default"
                    class="gui-button-mini xhs-border-radius50"
                    style="
                      width: 150rpx;
                      margin-right: 20rpx;
                      background: transparent;
                      backdrop-filter: blur(10px);
                      background-color: #d9f700;
                    "
                  >
                    <text class="gui-icons">回关</text>
                  </button>
                  <button
                    v-show="!author.isFollow && !author.isFollowMe"
                    @tap="followAuthor"
                    type="default"
                    class="gui-button-mini xhs-border-radius50"
                    style="
                      width: 150rpx;
                      margin-right: 20rpx;
                      background: transparent;
                      backdrop-filter: blur(10px);
                      background-color: #d9f700;
                    "
                  >
                    <text class="gui-icons">关注</text>
                  </button>

                  <button
                    @tap="goChat(author)"
                    class="gui-button-mini xhs-border-radius50 xhs-border-white"
                    style="
                      width: 82rpx;
                      background: transparent;
                      backdrop-filter: blur(10px);
                      background-color: rgba(255, 255, 255, 0.1);
                    "
                  >
                    <text class="gui-color-white gui-icons">&#xe6b8;</text>
                  </button>

                  <view>
                    <gui-popup-menu
                      @hideMenu="hideMenu"
                      ref="gracePopupMenu"
                      background="transparent"
                    >
                      <view
                        class="gui-bg-white gui-dark-bg-level-3"
                        style="border-radius: 8rpx; padding: 15rpx"
                      >
                        <text
                          class="menus gui-block gui-primary-text"
                          @tap.stop="tap"
                          data-id="1"
                          >个人资料</text
                        >
                        <text
                          class="menus gui-block gui-primary-text"
                          @tap.stop="tap"
                          data-id="2"
                          >退出登录</text
                        >
                      </view>
                    </gui-popup-menu>
                  </view>
                </view>
              </view>

              <view style="height: 10rpx"></view>
            </view>
          </view>
        </view>

        <!-- 模拟一个内容区域 假的吸顶容器默认不在最顶部 -->
        <!-- 动态产生一个 view 当导航吸顶时展示占位 高度等于 导航的高度  -->
        <view style="height: 55rpx" v-if="isFixed"></view>

        <gui-switch-navigation-mine
          lineHeight="60rpx"
          style="width: 100%"
          id="header"
          :class="[isFixed ? 'gui-fixed-top' : '', 'gui-bg-white']"
          :activeLineClass="['gui-xhs-red']"
          :items="tabs"
          :size="150"
          activeDirection="center"
          textAlign="center"
          :currentIndex="currentIndex"
          @change="navchange1"
        >
        </gui-switch-navigation-mine>

        <view class="gui-margin-top">
          <!-- 局部选项卡 使用 切换导航 + swiper 实现 -->
          <swiper
            :style="{ height: swiperHeight + 'px' }"
            :current="currentIndex"
            @change="swiperChange"
            @transition="swiperTransition"
          >
            <swiper-item>
              <view id="content-wrap1" style="padding: 0 15rpx">
                <gui-empty
                  v-show="noteList1[0].length == 0"
                  style="height: 500rpx"
                >
                  <template v-slot:img>
                    <view class="gui-flex gui-rows gui-justify-content-center">
                      <text
                        class="gui-icons gui-empty-icon gui-color-gray"
                        style="font-size: 80rpx"
                        >&#xe624;
                      </text>
                    </view>
                  </template>
                  <template v-slot:text>
                    <text
                      class="gui-block gui-text-center xhs-margin-top-30 gui-color-gray"
                      >TA还没有发布笔记哦~</text
                    >
                  </template>
                </gui-empty>
                <view class="gui-flex gui-row gui-space-between">
                  <view
                    class="gui-waterfall-item"
                    v-for="(lists, index1) in noteList1"
                    :key="index1"
                  >
                    <view
                      @tap="goDetail(notes)"
                      class="gui-waterfall-items gui-bg-white gui-border-radius"
                      style="overflow: hidden; padding-bottom: 8rpx"
                      v-for="(notes, index2) in lists"
                      :key="index2"
                    >
                      <gui-image
                        @imgLoad="imgLoad"
                        :src="notes.firstPicture"
                        :width="355"
                      ></gui-image>
                      <view
                        class="gui-text gui-primary-text xhs-margin-top-15 xhs-font30"
                        style="color: #333333; margin-left: 16rpx"
                      >
                        <text>{{ notes.noteTitle }}</text>
                      </view>
                      <view
                        class="gui-flex gui-row gui-space-between xhs-margin-top-15"
                      >
                        <view>
                          <image
                            style="
                              width: 28px;
                              height: 28px;
                              margin-left: 16rpx;
                            "
                            class="gui-list-image ucenter-face-image"
                            :src="notes.authorAvatar"
                            mode="aspectFill"
                          ></image>
                        </view>
                        <!--负值越小越往左-->
                        <view
                          style="
                            font-size: 24rpx;
                            color: #5a5a5a;
                            line-height: 60rpx;
                            width: 200rpx;
                          "
                        >
                          <text>{{ notes.authorName }}</text>
                          <!--<br/> <text style="color:#ACACAC;">{{notes.createTimeStr}}</text>-->
                        </view>

                        <view
                          style="
                            margin-right: 16rpx;
                            color: #acacac;
                            line-height: 42rpx;
                            font-size: 28rpx;
                            text-align: center;
                          "
                        >
                          <text
                            :class="[
                              'gui-icons',
                              'gui-block',
                              'ranking-lists-zan-icon',
                              notes.isUp ? 'zan-active' : '',
                            ]"
                          >
                            {{ notes.isUp ? "&#xe605;" : "&#xe6ad;" }}
                            <text style="color: #5a5a5a; margin-left: 5rpx">
                              {{ notes.upCount }}</text
                            >
                          </text>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </swiper-item>
            <swiper-item>
              <view class="components">
                <view v-for="(item, index) in petList" :key="index">
                  <view class="mainBox" v-if="item.show">
                    <!-- 基本信息 -->
                    <view class="basicInfo">
                      <view class="petInfoBox lineBox">
                        <view class="leftPetImg">
                          <image
                            src="/static/my/pet.png"
                            mode="aspectFit"
                          ></image>
                        </view>
                        <view class="rightBox">
                          <view class="form-item lineBox">
                            <text class="title">基本信息</text>
                            <!--<image-->
                            <!--  class="editTextImg"-->
                            <!--  @click="editPetInfo(item)"-->
                            <!--  src="/static/my/editText.png"-->
                            <!--  mode="aspectFit"-->
                            <!--&gt;</image>-->
                          </view>
                          <view class="form-item lineBox">
                            <text class="form-label">姓名：</text>
                            <text class="form-value">{{ item.name }}</text>
                          </view>
                          <view class="form-item lineBox">
                            <text class="form-label">性别：</text>
                            <text class="form-value">{{ item.gender }}</text>
                          </view>
                          <view class="form-item lineBox">
                            <text class="form-label">年龄：</text>
                            <view class="from-right" style="text-align: right">
                              <view
                                class="form-value"
                                style="text-align: right"
                              >
                                {{ item.age }}岁
                              </view>
                              <view class="form-label" style="text-align: right"
                                >约等于人的{{ item.aboutYearsOld }}岁
                              </view>
                            </view>
                          </view>
                          <view class="form-item lineBox">
                            <text class="form-label">出生日期：</text>
                            <text class="form-value">{{ item.birthDate }}</text>
                          </view>
                          <view class="form-item lineBox">
                            <text class="form-label">品种：</text>
                            <text class="form-value">{{ item.breed }}</text>
                          </view>
                        </view>
                      </view>
                      <view class="form-bottom lineBox">
                        <view class="form-item">
                          <text class="form-label">最新身价：</text>
                          <text class="form-value"
                            >￥{{ item.latestValue }}</text
                          >
                        </view>
                        <view class="form-item">
                          <text class="form-label">已陪伴：</text>
                          <text class="form-value"
                            >{{ item.accompanyDays }}天</text
                          >
                        </view>
                      </view>
                    </view>

                    <!-- 宠物日记 -->
                    <view class="diaryBox">
                      <view class="title1">
                        <view class="">宠物日记</view>
                        <!--<view class="" @click="postDiary(item)">发日记</view>-->
                      </view>
                      <view class="shopListMain">
                        <view
                          class="leftList"
                          v-for="(fitem, findex) in noteList"
                          :key="findex"
                        >
                          <view
                            v-for="(item, index) in fitem"
                            :key="index"
                            class="card"
                          >
                            <!-- image 是否是图片 1是图片 2不是图片， -->
                            <view
                              v-if="item.noteType === 1"
                              class="image-container"
                              @click="goDetail(item)"
                            >
                              <image
                                :src="item.firstPicture"
                                class="image"
                                mode="widthFix"
                              ></image>
                              <text class="DirartText">{{
                                item.noteTitle
                              }}</text>
                            </view>
                            <view
                              v-else
                              class="video-container"
                              :style="{ height: item.height + 'rpx' }"
                              @click="goDetail(item)"
                            >
                              <image
                                :src="item.firstPicture"
                                class="image"
                              ></image>
                              <image
                                class="bofangImg"
                                src="/static/my/bofangIcon.png"
                                mode=""
                              ></image>
                              <text class="DirartText">{{
                                item.noteTitle
                              }}</text>
                            </view>
                          </view>
                          <view v-if="findex == 1">
                            <view
                              class="moreText"
                              v-if="hasMore"
                              @click="loadMore"
                            >
                              查看更多 >
                            </view>
                          </view>
                        </view>
                      </view>
                    </view>

                    <!-- 身体数据概况 -->
                    <view class="bodyData">
                      <view class="title1"> 身体数据概况 </view>
                      <view class="bodyData-box">
                        <view class="form-item w300 lineBox">
                          <text class="form-label">体重：</text>
                          <text class="form-value">{{ item.weight }}KG</text>
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">腿长：</text>
                          <text class="form-value">{{ item.legLength }}CM</text>
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">头围：</text>
                          <text class="form-value"
                            >{{ item.headCircumference }}CM</text
                          >
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">尾长：</text>
                          <text class="form-value"
                            >{{ item.tailLength }}CM</text
                          >
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">腰围：</text>
                          <text class="form-value"
                            >{{ item.waistCircumference }}CM</text
                          >
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">爪子颜色：</text>
                          <text class="form-value">{{ item.pawColor }}</text>
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">颈围：</text>
                          <text class="form-value"
                            >{{ item.neckCircumference }}CM</text
                          >
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">眼睛颜色：</text>
                          <text class="form-value">{{ item.eyeColor }}</text>
                        </view>
                        <view class="form-item w300 lineBox">
                          <text class="form-label">身长：</text>
                          <text class="form-value"
                            >{{ item.bodyLength }}CM</text
                          >
                        </view>
                      </view>
                    </view>
                    <!-- 性格行为习惯展示 -->
                    <view class="natureBox">
                      <view class="title1"> 性格行为习惯展示 </view>
                      <view class="natureText">{{
                        item.personalityHabit
                      }}</view>
                    </view>
                    <!-- 病历 -->
                    <!--<view class="medicalRecord">-->
                    <!--  <view class="title"> 病历 </view>-->
                    <!--  <view class="moreText" @click="moreMedicalRecord(item)">-->
                    <!--    点击显示>-->
                    <!--  </view>-->
                    <!--</view>-->
                  </view>

                  <view class="mainBoxHide" v-else @click="showPet(item)">{{
                    item.name
                  }}</view>
                </view>
                <!--<view class="addBtn" @click="editPetInfo()"-->
                <!--  >+ 宠物 {{ petList.length + 1 }}</view-->
                <!--&gt;-->
              </view>
            </swiper-item>
          </swiper>
        </view>

        <!-- 加载组件 -->
        <gui-loadmore
          v-show="currentIndex == 0 && noteList1[0].length != 0"
          ref="loadmorecom1"
          :status="1"
        ></gui-loadmore>
      </view>
    </template>
  </gui-page>
</template>
<script>
import commonMixin from "@/mixin/commonMinxin";
import logo from "@/components/logo/logo.vue";

export default {
  mixins: [commonMixin],
  data() {
    return {
      defaultSwiperHeight: 0,
      loaded: false,
      authorId: "",
      author: {
        avatarUrl: "",
        backGroundUrl: "http://*************:8080/upload/notes/note (6).jpg",
      },
      backgroundUrl: '"http://*************:8080/upload/notes/note (6).jpg"',
      isFixed: false,
      navItems: [
        {
          title: "购物",
          desc: "好逛好玩又好买",
        },
        {
          title: "订单",
          desc: "查看我的订单",
        },
        {
          title: "购物车",
          desc: "99个商品",
        },
        {
          title: "创作灵感",
          desc: "学创作找灵感",
        },
        {
          title: "浏览记录",
          desc: "我看过的笔记",
        },
      ],
      // 选项卡标签
      tabs: [
        {
          id: 0,
          name: "笔记",
        },
        {
          id: 1,
          name: "宠物档案",
        },
      ],
      // 选中选项的 索引
      currentIndex: 0,
      noteList1: [[], []],
      noteList2: [[], []],
      noteList3: [[], []],
      // 用于记录是否有 api 请求正在执行
      apiLoadingStatus1: false,
      apiLoadingStatus2: false,
      apiLoadingStatus3: false,
      page1: 1,
      page2: 1,
      page3: 1,
      swiperHeight: 500,
      headerHeight: 0,
      pageHeight: "",
      loadMoreTimer1: null,
      loadMoreTimer2: null,
      loadMoreTimer3: null,

      petList: [],
      noteList: [[], []],
      columnHeights: [0, 0], // 记录每列累计高度
      page: 1, // 页码变量
      hasMore: true, // 是否有更多数据标志
      apiLoadingStatus: false, // 加载状态标志
      userId: "",
    };
  },
  watch: {
    currentIndex(newVal, OldVal) {
      this.$nextTick(() => {
        this.setSwiperHeight(this.currentIndex + 1);
      });
    },
    // 监听笔记数据变化，确保数据加载后重新计算高度
    noteList1: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          if (this.currentIndex === 0) {
            this.setSwiperHeight(1);
          }
        });
      },
    },
    noteList: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          if (this.currentIndex === 1) {
            this.setSwiperHeight(2);
          }
        });
      },
    },
    petList: {
      deep: true,
      handler() {
        this.$nextTick(() => {
          if (this.currentIndex === 1) {
            this.setSwiperHeight(2);
          }
        });
      },
    },
  },
  onHide() {
    this.isFixed = false;
  },
  onShow() {
    console.log("onShow");
    //证明不是第一次进页面了 刷新当前页
    if (this.loaded) {
      this.$nextTick(() => {
        // this['page'+(this.currentIndex+1)] = 1;
        // this['noteList'+(this.currentIndex+1)] = [[],[]]
        // this['getNotes'+(this.currentIndex+1)](true);
      });
    }
  },
  onLoad: function (e) {
    uni.pageScrollTo({
      scrollTop: 0,
      duration: 0,
    });
    // uni.app.checkLogin()

    this.$nextTick(() => {
      // this.authorId = "e0a5f799bbd2499a8205958689a670cd"

      this.authorId = e.authorId;
      console.log(e.authorId);
      this.userId = e.userId;
      console.log(this.userId);

      this.getAuthor();
      const systemInfo = uni.getSystemInfoSync();
      this.pageHeight = systemInfo.windowHeight;
      console.log("当前页面高度：", this.pageHeight);
      // 创建查询对象
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        // 选择元素并获取位置信息
        query
          .select("#header")
          .boundingClientRect((data) => {
            if (data) {
              console.log(data);
              // data.top就是元素的上边界距离页面顶部的距离
              const distanceToTop = data.top;
              console.log("距离顶部的高度:", distanceToTop);
              if (this.headerHeight == 0) {
                this.headerHeight = data.top;
                console.log(this.headerHeight);
              }
              this.getNotes1();
              // this.getNotes2();
              // this.getNotes3();
              this.loaded = true;
              // this.swiperHeight = this['swiperHeight'+(this.currentIndex+1)]
              // console.log('赋值了高度 当前currentIndex+1='+ (this.currentIndex+1) +' onLoad监听的数据' + this.swiperHeight)

              //此时 swiper的高度等于 页面高度 减去头部高度
              this.swiperHeight = this.pageHeight - this.headerHeight;
              this.defaultSwiperHeight = this.pageHeight - this.headerHeight;
            }
          })
          .exec();
      }, 200);
    });

    this.getPet();
    // 移除这里的getNotes调用，因为已经在getPet方法的回调中调用了
  },
  onPageScroll: function (e) {
    console.log(e);
    // 根据滚动高度动态吸顶
    if (e.scrollTop >= this.headerHeight) {
      this.isFixed = true;
    } else {
      this.isFixed = false;
    }
  },
  onPullDownRefresh: function () {
    console.log("下拉刷新了");
    if (this["apiLoadingStatus" + (this.currentIndex + 1)]) {
      uni.stopPullDownRefresh();
      return;
    }
    this["page" + (this.currentIndex + 1)] = 1;
    this["noteList" + (this.currentIndex + 1)] = [[], []];
    this["getNotes" + (this.currentIndex + 1)](true);
  },
  onReachBottom: function () {
    console.log("我到第" + (this.currentIndex + 1) + "最低端了");
    console.log(this["apiLoadingStatus" + (this.currentIndex + 1)]);
    if (this["apiLoadingStatus" + (this.currentIndex + 1)]) {
      return;
    }
    // 获取加载组件状态看一下是否还能继续加载
    // 保证触底只执行一次加载
    console.log("timer");
    console.log(this["loadMoreTimer" + (this.currentIndex + 1)]);
    if (this["loadMoreTimer" + (this.currentIndex + 1)] != null) {
      clearTimeout(this["loadMoreTimer" + (this.currentIndex + 1)]);
    }
    this["loadMoreTimer" + (this.currentIndex + 1)] = setTimeout(() => {
      // 确保引用存在
      const loadmoreRef = this.$refs["loadmorecom" + (this.currentIndex + 1)];
      if (!loadmoreRef) {
        return;
      }

      var status = loadmoreRef.loadMoreStatus;
      if (status != 0) {
        return null;
      }
      loadmoreRef.loading();
      // 此处开启加载动画执行加载数据的函数
      this["getNotes" + (this.currentIndex + 1)]();
    }, 80);
  },
  methods: {
    goBack: function () {
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          window.AndroidBridge.backEvent();
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.backEvent.postMessage({});
          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
    formatImgUrl(imgUrl) {
      return '"' + imgUrl + '"';
    },
    goChat(author) {
      uni.app.post(
        "/blog/chat/addFriend",
        {
          authorId: uni.getStorageSync("userId"),
          addAuthorId: this.authorId,
        },
        "json",
        "",
        (res) => {
          // this.getAuthor();
          this.goPage();
        },
      );
    },
    goFollow(e, type) {
      console.log(e);
      uni.navigateTo({
        url: "/pages/users/users?authorId=" + e.userId + "&type=" + type,
      });
    },
    cancelfollowAuthor() {
      uni.app.post(
        "/blog/action/cancelFollowAuthor",
        {
          authorId: uni.getStorageSync("userId"),
          followAuthorId: this.authorId,
        },
        "json",
        "",
        (res) => {
          console.log(res.data);

          this.getAuthor();
        },
      );
    },
    followAuthor() {
      uni.app.post(
        "/blog/action/followAuthor",
        {
          followAuthorId: this.authorId,
          authorId: uni.getStorageSync("userId"),
        },
        "json",
        "",
        (res) => {
          console.log(res.data);

          this.getAuthor();
        },
      );
    },
    goDetail(e) {
      if (e.noteType == 1) {
        uni.navigateTo({
          url: "/pages/notes/detail/detail?noteId=" + e.noteId,
        });
      } else if (e.noteType == 2) {
        uni.navigateTo({
          url: "/pages/notes/videoDetail/videoDetail?noteId=" + e.noteId,
        });
      }
    },
    upNote(notes) {
      uni.app.post("/auth/upNote", notes, "json", "", (res) => {
        console.log(res.data);
        if (res.data) {
          notes.isUp = true;
          notes.upCount++;
        } else {
          notes.isUp = false;
          notes.upCount--;
        }
      });
    },
    imgLoad() {
      console.log("图片加载完成了");
      // 使用requestAnimationFrame确保在下一帧渲染前更新高度
      requestAnimationFrame(() => {
        this.setSwiperHeight(this.currentIndex + 1);
      });
    },
    getAuthor() {
      let that = this;
      uni.app.post(
        "/blog/author/getAuthor",
        {
          followAuthorId: this.authorId,
          authorId: uni.getStorageSync("userId"),
        },
        "",
        "",
        (res) => {
          this.author = res.data;
          this.backGroundUrl = res.data.backGroundUrl;
          uni.setNavigationBarTitle({
            title: this.author.authorName + " 的个人资料",
          });
        },
      );
    },
    goPage() {
      console.log(this.author.imId);
      console.log(uni.getStorageSync("loginInfo").accessToken);
      console.log(this.userId);
      console.log(
        `https://www.yaozhuasocial.com/yaozhua_w/#/pages/chat/chat-box?targetId=${this.author.imId}&accessToken=${uni.getStorageSync("loginInfo").accessToken}&userId=${this.userId}`,
      );
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          let ddd = {
            hideNavBar: "1",
            hideTotalNavBar: "1",
            hideBottomSafeBar: "1",
            navtitle: "聊天",
            url: `https://www.yaozhuasocial.com/yaozhua_w/#/pages/chat/chat-box?targetId=${this.author.imId}&accessToken=${uni.getStorageSync("loginInfo").accessToken}&userId=${this.userId}`,
          };
          let d = JSON.stringify(ddd);
          console.log(d, "都是对的");
          window.AndroidBridge.pushNewWebVC(d);
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.pushNewWebVC.postMessage({
            hideNavBar: "1",
            hideTotalNavBar: "1",
            hideBottomSafeBar: "1",
            navtitle: "聊天",
            url: `https://www.yaozhuasocial.com/yaozhua_w/#/pages/chat/chat-box?targetId=${this.author.imId}&accessToken=${uni.getStorageSync("loginInfo").accessToken}&userId=${this.userId}`,
          });

          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
    showMenu: function () {
      this.$refs.gracePopupMenu.open();
      this.$refs.gracePopupMenu.setTop(10);
      this.$refs.gracePopupMenu.setRight(4);
    },
    hideMenu: function () {
      this.$refs.gracePopupMenu.close();
    },
    tap: function (e) {
      var id = e.currentTarget.dataset.id;
      // uni.showToast({ title:'您点击了菜单 : ' + id, icon:"none"});
      if (id == 2) {
        uni.app.removeStorage("token");
        uni.navigateTo({
          url: "/pages/login/login",
        });
      }
      this.$refs.gracePopupMenu.close();
    },
    //此方法可以实现一部分 当滑动到 swiper页面很生硬
    // 准备另外一种方法 获取屏幕高度 然后减去header的高度 当页面滑动到header 增加 swiper的高度
    //依然不行 因为guipage有一个向下滑动的动作 滑动后才进行动态数值 会造成页面抖动
    //用第三种 原生 可以
    // swiperScroll(e){
    // 	console.log(e.detail.scrollTop)
    // 	console.log(this.headerHeight)
    // 	if(e.detail.scrollTop>0){
    // 		uni.pageScrollTo({
    // 			scrollTop: this.headerHeight,
    // 			duration: 0
    // 		});
    // 	}
    // },
    //动态设置swiper的高度
    setSwiperHeight(i) {
      var swiperHeightTimer = null;
      if (swiperHeightTimer != null) {
        clearTimeout(swiperHeightTimer);
      }
      swiperHeightTimer = setTimeout(() => {
        // 如果是宠物档案选项卡(index为2)
        if (i === 2) {
          // 查询宠物档案内容的实际高度
          let query = uni.createSelectorQuery().in(this);
          query.select(".components").boundingClientRect();
          query.exec((res) => {
            if (res && res[0]) {
              // 使用实际内容高度，并添加一些额外空间
              this.swiperHeight = Math.max(
                res[0].height + 100,
                this.defaultSwiperHeight,
              );
              console.log(
                "设置宠物档案选项卡高度：" + this.swiperHeight + "px",
              );
            } else {
              // 如果无法获取高度，使用默认高度
              this.swiperHeight = this.defaultSwiperHeight;
            }
          });
          return;
        }

        let element = "#content-wrap" + i.toString();
        let query = uni.createSelectorQuery().in(this);
        query.select(element).boundingClientRect();
        query.exec((res) => {
          if (res && res[0]) {
            console.log("当前Swiper" + i + "高度" + res[0].height);
            // 强制重新计算，如果当前内容小于默认高度，则使用默认高度
            if (
              res[0].height == 0 ||
              res[0].height < this.defaultSwiperHeight
            ) {
              this.swiperHeight = this.defaultSwiperHeight;
            } else {
              // 确保使用实际内容高度
              this.swiperHeight = res[0].height;
            }
            console.log(
              "设置swiper高度：" + this.swiperHeight + "px，当前选项卡：" + i,
            );
          } else {
            // 如果没有获取到元素，使用默认高度
            this.swiperHeight = this.defaultSwiperHeight;
          }
        });
      }, 50); // 增加延迟，确保DOM已更新
    },
    swiperTransition(e) {
      // 在swiper过渡期间也更新高度，确保切换到不同内容的选项卡时高度正确
      this.$nextTick(() => {
        this.setSwiperHeight(this.currentIndex + 1);
      });
    },
    reload: function () {
      console.log(this.currentIndex);
      //根据当前index 确定清空的对象
      this["page" + (this.currentIndex + 1)] = 1;
      this["noteList" + (this.currentIndex + 1)].splice(0, 1, []);
      this["noteList" + (this.currentIndex + 1)].splice(1, 1, []);
      this["getNotes" + (this.currentIndex + 1)](1);
    },
    // 名片切换
    navchange: function (index) {
      console.log(index);
    },
    navchange1: function (index) {
      if (index == 4) {
        uni.app.msg("搜索");
      } else {
        this.currentIndex = index;
        this.setSwiperHeight(this.currentIndex + 1);
      }
    },
    swiperChange: function (e) {
      var index = e.detail.current;
      this.currentIndex = index;
      this.setSwiperHeight(this.currentIndex + 1);
    },
    getNotes1(isReload) {
      uni.app.showLoading("获取中 ...");
      console.log("我开始请求了1");
      this.apiLoadingStatus1 = true;
      const that = this;
      let params = {
        authorId: this.authorId,
        followId: uni.getStorageSync("userId"),
        limit: 10,
        offset: that.page1, // 修正
      };
      uni.app.post("/blog/note/getAuthorNotes", params, "", "", (res) => {
        uni.hideLoading();
        if (res.code == 2000) {
          if (that.page1 <= res.data.pages) {
            let notes = res.data.records;

            var lArr = that.noteList1[0];
            var rArr = that.noteList1[1];
            //填充数组[此处的 notes 数据应该来自与api接口数据]
            //数据格式见 "/data/data.js"
            for (var i = 0; i < notes.length; i++) {
              if (i % 2 == 0) {
                lArr.push(notes[i]);
              } else {
                rArr.push(notes[i]);
              }
            }
            that.noteList1 = [lArr, rArr];
            this.$nextTick(() => {
              this.setSwiperHeight(1);
            });

            if (that.page1 == res.data.pages) {
              //停止加载更多
              if (this.$refs.loadmorecom1) {
                this.$refs.loadmorecom1.nomore();
              }
            } else {
              that.page1++;
              //停止加载更多
              if (this.$refs.loadmorecom1) {
                this.$refs.loadmorecom1.stoploadmore();
              }
            }
            that.apiLoadingStatus1 = false;
          } else if (res.data.pages == 0) {
            that.apiLoadingStatus1 = false;
            if (this.$refs.loadmorecom1) {
              this.$refs.loadmorecom1.nomore();
            }
          }

          if (isReload) {
            uni.stopPullDownRefresh();
          }
        }
      });
    },

    getPet() {
      uni.app.post(
        "/pet/info/getInfos",
        {
          userId: this.authorId,
          offset: 1,
          limit: 10,
        },
        "json",
        "",
        (res) => {
          this.petList = res.data.records;
          this.petList.forEach((item, idx) => {
            item.show = idx === 0;
            item.birthDate = this.timestampToTime(item.birthDate);
          });
          // 获取宠物列表后，加载第一个宠物的日记
          this.getNotes();
        },
      );
    },
    getNotes(isReload) {
      const that = this;
      if (isReload) {
        this.page = 1;
        this.noteList = [[], []];
        this.columnHeights = [0, 0];
      }
      this.apiLoadingStatus = true;

      // 获取当前显示的宠物ID
      const selectedPet = this.petList.find((pet) => pet.show);
      const petId = selectedPet ? selectedPet.id : "";

      let params = {
        authorId: uni.getStorageSync("userId"),
        followId: this.authorId,
        petId: Number(petId),
        noteCategory: 1,
        limit: 4,
        offset: this.page,
      };
      uni.app.post("/blog/note/getNotes", params, "", "", (res) => {
        this.apiLoadingStatus = false;
        if (res.code == 2000) {
          let notes = res.data.records || [];
          if (notes.length > 0) {
            for (var i = 0; i < notes.length; i++) {
              // 获取当前卡片高度，根据图片比例计算
              let itemHeight = 300; // 默认高度
              if (notes[i].firstPictureHeight && notes[i].firstPictureWidth) {
                // 计算宽高比，考虑宽度固定为列宽的一半
                const columnWidth = 330; // 假设列宽为屏幕宽度的一半(750/2 - 间距)
                const aspectRatio =
                  notes[i].firstPictureHeight / notes[i].firstPictureWidth;
                itemHeight = columnWidth * aspectRatio;
              }
              notes[i].height = itemHeight;

              // 找到当前高度最小的列
              let minIndex =
                that.columnHeights[0] <= that.columnHeights[1] ? 0 : 1;
              that.noteList[minIndex].push(notes[i]);
              that.columnHeights[minIndex] += itemHeight;
            }
            that.page++;
            that.hasMore = that.page <= res.data.pages;
          } else {
            that.hasMore = false;
          }
        }
      });
    },
    // 添加加载更多方法
    loadMore() {
      if (this.hasMore && !this.apiLoadingStatus) {
        // 获取当前显示的宠物ID
        const selectedPet = this.petList.find((pet) => pet.show);
        const petId = selectedPet ? selectedPet.id : "";

        uni.navigateTo({
          url: `/pages/petDiary/petDiary?petId=${petId}`,
        });
      }
    },
    showPet(item) {
      // 先全部设为 false
      this.petList.forEach((pet) => {
        pet.show = false;
      });
      // 当前 item 设为 true
      item.show = true;

      // 重置日记数据并重新加载当前宠物的日记
      this.page = 1;
      this.noteList = [[], []];
      this.columnHeights = [0, 0];
      this.getNotes(true);
    },
    editPetInfo(item) {
      if (item) {
        let b = {
          hideNavBar: "0",
          hideTotalNavBar: "1",
          hideBottomSafeBar: "1",
          navtitle: "编辑宠物档案",
          url: `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/my/editPetProfile?petId=${item.id}&token=${uni.getStorageSync("token")}&userId=${uni.getStorageSync("userId")}`,
        };
        this.fn(b);
      } else {
        let a = {
          hideNavBar: "0",
          hideTotalNavBar: "1",
          hideBottomSafeBar: "1",
          navtitle: "编辑宠物档案",
          url: `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/my/editPetProfile?token=${uni.getStorageSync("token")}&userId=${uni.getStorageSync("userId")}`,
        };
        this.fn(a);
      }
    },
    postDiary(item) {
      let w = { id: item.id };
      this.fn1(w);
    },
    moreMedicalRecord(item) {
      let data = {
        hideNavBar: "1",
        hideTotalNavBar: "1",
        hideBottomSafeBar: "1",
        navtitle: "宠物病历",
        url: `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/my/editPetMedicalRecord?petId=${item.id}&token=${uni.getStorageSync("token")}&userId=${uni.getStorageSync("userId")}`,
      };
      this.fn(data);
    },
    fn(data) {
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          let ddd = data;
          let d = JSON.stringify(ddd);
          console.log(d, "都是对的");
          window.AndroidBridge.pushNewWebVC(d);
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.pushNewWebVC.postMessage(data);
          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
    fn1(data) {
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          let ddd = data;
          let d = JSON.stringify(ddd);
          console.log(d, "都是对的");
          window.AndroidBridge.petPublishEvent(d);
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.petPublishEvent.postMessage(data);
          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
    timestampToTime(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      const Y = date.getFullYear();
      const M =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      const D = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      return `${Y}-${M}-${D}`;
    },
  },
};
</script>
<style lang="scss" scoped>
/*数值越大屏幕中边距越小 左右模块的宽度*/
.gui-waterfall-item {
  width: 355rpx;
}

.gui-waterfall-items {
  margin-bottom: 15rpx;
}

.gui-block {
  margin-top: 10rpx;
}

.ucenter-face {
  width: 100rpx;
  height: 100rpx;
}

.ucenter-face-image {
  width: 100rpx;
  height: 100rpx;
}

.gui-list-title-text {
  line-height: 60rpx;
}

.gui-fixed-top {
  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 750rpx;
}

/* #ifdef  H5 */
.gui-fixed-top {
  top: 80rpx !important;
}

/* #endif */
/* #ifdef  MP */
.gui-fixed-top {
  top: 150rpx !important;
}

/* #endif */
.bg {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.bg-element {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 25%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.5) 70%,
    rgba(0, 0, 0, 0.4) 100%
  );
  /* 解释：linear-gradient表示创建线性渐变，to top指定渐变方向为从下往上，
        rgba(0, 0, 0, 0.8) 0%表示在起始位置（底部）颜色为透明度0.8的黑色，
        rgba(0, 0, 0, 0) 100%表示在结束位置（顶部）颜色为完全透明的黑色，实现透明度减弱效果 */
}

.author-tag {
  height: 50rpx;
  line-height: 50rpx;
  border-radius: 50rpx;
  padding: 0 30rpx;
  margin-right: 20rpx;
}

.components {
  .lineBox {
    border-bottom: 2rpx solid #f8f8f8;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    padding: 15rpx 0;

    .form-label {
      font-weight: 500;
      font-size: 24rpx;
      color: #7f7f7f;
    }

    .form-value {
      font-weight: bold;
      font-size: 26rpx;
      color: #303030;
    }

    .from-right {
      flex: 1;
    }

    &:last-child {
      border-bottom: none;
    }
  }

  .form-header {
    padding: 0 0 15rpx;
  }

  .form-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 15rpx;
  }

  .mainBox {
    background-color: #ffffff;
    margin: 0 28rpx 20rpx;
    padding: 32rpx;
    border-radius: 10rpx;

    .basicInfo {
      .petInfoBox {
        display: flex;
        align-items: center;

        .leftPetImg {
          width: 300rpx;
          height: 402rpx;
          border-radius: 16rpx;

          image {
            width: 100%;
            height: 100%;
          }
        }

        .rightBox {
          flex: 1;
          margin-left: 32rpx;
        }

        .title {
          font-weight: bold;
          font-size: 30rpx;
          color: #303030;
          line-height: 40rpx;
        }

        .editTextImg {
          width: 80rpx;
          height: 34rpx;
        }
      }
    }

    .diaryBox {
      .title1 {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 48rpx 0 16rpx;
        font-weight: bold;
        font-size: 30rpx;
        color: #303030;
        line-height: 40rpx;

        :nth-child(2) {
          background-color: #d9f700;
          padding: 0 40rpx;
          border-radius: 50rpx;
          height: 48rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
        }
      }
    }

    .bodyData {
      .title1 {
        margin: 48rpx 0 16rpx;
        font-weight: bold;
        font-size: 30rpx;
        color: #303030;
        line-height: 40rpx;
      }

      .bodyData-box {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        .form-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: calc(50% - 20rpx);

          .form-label {
            font-weight: 500;
            font-size: 24rpx;
            color: #7f7f7f;
          }

          .form-value {
            font-weight: bold;
            font-size: 26rpx;
            color: #303030;
          }
        }
      }
    }

    .natureBox {
      .title1 {
        margin: 48rpx 0 16rpx;
        font-weight: bold;
        font-size: 30rpx;
        color: #303030;
        line-height: 40rpx;
      }

      .natureText {
        font-weight: bold;
        font-size: 26rpx;
        color: #303030;
        line-height: 38rpx;
        padding: 16rpx 0 0 0;
        border-top: 2rpx solid #f8f8f8;
      }
    }

    .medicalRecord {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 32rpx;

      .title {
        font-weight: bold;
        font-size: 30rpx;
        color: #303030;
        line-height: 40rpx;
      }

      .moreText {
        font-weight: 400;
        font-size: 24rpx;
        color: #c69c6d;
      }
    }
  }

  .mainBoxHide {
    background-color: #ffffff;
    margin: 0 28rpx 20rpx;
    height: 64rpx;
    border-radius: 10rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .addBtn {
    background-color: #d9f700;
    margin: 20rpx 28rpx 0;
    height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10rpx;
  }

  .shopListMain {
    display: flex;
    justify-content: space-between;

    .leftList {
      width: 49%;
    }

    .rightList {
      width: 49%;
    }

    .moreText {
      font-weight: 400;
      font-size: 24rpx;
      color: #c69c6d;
      margin: 60rpx auto 0;
      text-align: center;
    }

    .card {
      width: 100%;
      margin-bottom: 14rpx;
      background-color: #fff;
      border-radius: 10rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      position: relative;
      overflow: hidden;
    }

    .image-container {
      overflow: hidden;
      position: relative;
      width: 100%;

      .image {
        width: 100%;
        height: auto;
        display: block;
      }
    }

    .video-container {
      overflow: hidden;
      position: relative;
      width: 100%;

      .image {
        width: 100%;
        height: 100%;
        display: block;
        object-fit: cover;
      }
    }

    .bofangImg {
      width: 32rpx;
      height: 32rpx;
      position: absolute;
      top: 16rpx;
      right: 16rpx;
    }

    .DirartText {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 16rpx 26rpx 24rpx;
      background-color: rgba(0, 0, 0, 0.35);
      font-weight: bold;
      font-size: 24rpx;
      color: #ffffff;
    }
  }
}
</style>
