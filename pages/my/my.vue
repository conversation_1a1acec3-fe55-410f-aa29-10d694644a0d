<template>
  <view>
    <!-- 顶部用户信息 -->
    <view class="topUserInfo">
      <view class="bgImg">
        <image
          :src="author.backGroundPic || '/static/my/<EMAIL>'"
          mode="aspectFit"
        ></image>
      </view>

      <view class="setImg" @click="setUserInfo">
        <image src="/static/my/<EMAIL>" mode="aspectFit"></image>
      </view>
      <view class="userBox">
        <view class="leftBox" @click="goPage('/pages/authorInfo/authorInfo')">
          <view class="headImg" @click="editDeadImg">
            <image :src="author.userPic" mode="aspectFill"></image>
          </view>
          <!--<view class="addImg" >-->
          <!--  <image src="/static/my/<EMAIL>" mode="aspectFill"></image>-->
          <!--</view>-->
        </view>
        <view class="infoBox">
          <view class="nameBox" @click="goPage('/pages/authorInfo/authorInfo')">
            <text>{{ author.nickname }}</text>
            <image
              src="/static/my/<EMAIL>"
              mode="aspectFit"
            ></image>
          </view>
          <view class="IPBox">
            <text v-if="author.ipRealAddress"
              >IP:{{ author.ipRealAddress }}
            </text>
            <text>摇爪号:{{ author.authorNo }}</text>
          </view>
          <view class="numBox">
            <text class="numBoxItem" @click="go(2)">
              <text class="f26">{{ author.fans }}</text>
              <text class="f16">粉丝</text>
            </text>
            <text class="numBoxItem" @click="go(1)">
              <text class="f26">{{ author.follow }}</text>
              <text class="f16">关注</text>
            </text>
            <text class="numBoxItem" @click="go(3)">
              <text class="f26">{{ author.upAndStarCount }}</text>
              <text class="f16">获赞</text>
            </text>
          </view>
        </view>
      </view>
      <view class="signBox" @click="editUserSign">
        <image src="/static/my/<EMAIL>" mode="aspectFit"></image>
      </view>
    </view>
    <!-- 切换tabs -->
    <view class="switchTabs">
      <view
        v-for="(item, index) in tabsList"
        :key="index"
        :class="['tabsBox', { actTabsBox: activeTabs === item.value }]"
        @click="switchTabs(item.value)"
      >
        {{ item.name }}
      </view>
    </view>
    <view class="">
      <!-- 宠物档案 -->
      <petProfile v-if="activeTabs == 1"></petProfile>
      <!-- 发布 -->
      <shopList style="" v-if="activeTabs == 2" :comPage="comPage"></shopList>
      <!-- 收藏 -->

      <collect v-if="activeTabs == 3" :comPage="comPage"></collect>
      <likeList v-if="activeTabs == 4" :comPage="comPage"></likeList>
    </view>
  </view>
</template>

<script>
import petProfile from "./my_compoments/petProfile.vue";
import shopList from "./my_compoments/shopList.vue";
import likeList from "./my_compoments/likeList.vue";
import collect from "./my_compoments/collect.vue";
import commonMixin from "@/mixin/commonMinxin";
export default {
  mixins: [commonMixin],
  components: {
    petProfile,
    shopList,
    likeList,
    collect,
  },
  data() {
    return {
      comPage: 1,
      author: {},
      activeTabs: 1,
      tabsList: [
        {
          name: "宠物档案",
          value: 1,
        },
        {
          name: "发布",
          value: 2,
        },
        {
          name: "收藏",
          value: 3,
        },
        {
          name: "赞过",
          value: 4,
        },
      ],
    };
  },
  onReachBottom() {
    this.comPage++;
    console.log(this.comPage);
  },
  onLoad(e) {
    this.getAuthor();
    if (e.userId) {
      uni.setStorageSync("userId", e.userId);
    } else if (e.token) {
      uni.setStorageSync("token", e.token);
    }
  },
  methods: {
    getAuthor() {
      let that = this;
      uni.app.post(
        "/blog/author/getAuthor",
        {
          followAuthorId: uni.getStorageSync("userId"),
          authorId: uni.getStorageSync("userId"),
        },
        "",
        "",
        (res) => {
          this.author = res.data;
          this.backGroundUrl = res.data.backGroundUrl;
          uni.setNavigationBarTitle({
            title: this.author.authorName + " 的个人资料",
          });
        },
      );
    },
    goFollow(e, type) {
      uni.navigateTo({
        url: "/pages/users/users1?authorId=" + e.authorId + "&type=" + type,
      });
    },
    setUserInfo() {
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          // let ddd = {
          // 	hideNavBar: '0',
          // 	hideTotalNavBar: '0',
          // 	hideBottomSafeBar: '0',
          // 	navtitle: '待使用',
          // 	url: 'http://192.168.101.147:8080/#/pages/hotspot/orderDetailSuccess'
          // }
          let d = "123";
          console.log(d, "都是对的");
          window.AndroidBridge.settingEvent(d);
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.settingEvent.postMessage("123");
          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
    editDeadImg() {
      console.log("切换头像");
    },
    editUserSign() {
      console.log("编辑用户签名");
    },
    switchTabs(value) {
      this.comPage = 1;
      // 切换按钮
      this.activeTabs = value;
    },
    go(type) {
      let url;
      if (type == 3) {
        url = `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/users/users1?type=0&authorId=${uni.getStorageSync("userId")}&token=${uni.getStorageSync("token")}`;
      } else {
        url = `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/users/users?type=${type}&authorId=${uni.getStorageSync("userId")}&token=${uni.getStorageSync("token")}`;
      }
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          let ddd = {
            hideNavBar: "1",
            hideTotalNavBar: "0",
            hideBottomSafeBar: "0",
            navtitle: "编辑个人资料",
            url: url,
          };
          let d = JSON.stringify(ddd);
          console.log(d, "都是对的");
          window.AndroidBridge.pushNewWebVC(d);
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.pushNewWebVC.postMessage({
            hideNavBar: "1",
            hideTotalNavBar: "0",
            hideBottomSafeBar: "0",
            navtitle: "编辑个人资料",
            url: url,
          });

          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
    copy() {
      uni.setClipboardData({
        data: this.url, // e是你要保存的内容
        success: function () {
          uni.showToast({
            title: "复制成功",
            icon: "none",
          });
        },
      });
    },
    goPage(url) {
      switch (uni.getSystemInfoSync().platform) {
        case "android":
          let ddd = {
            hideNavBar: "0",
            hideTotalNavBar: "0",
            hideBottomSafeBar: "0",
            navtitle: "编辑个人资料",
            url: `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/authorInfo/authorInfo?authorId=${uni.getStorageSync(
              "userId",
            )}&token=${uni.getStorageSync("token")}`,
          };
          let d = JSON.stringify(ddd);
          console.log(d, "都是对的");
          window.AndroidBridge.pushNewWebVC(d);
          console.log("运行Android上");
          break;
        case "ios":
          window.webkit.messageHandlers.pushNewWebVC.postMessage({
            hideNavBar: "0",
            hideTotalNavBar: "0",
            hideBottomSafeBar: "0",
            navtitle: "编辑个人资料",
            url: `https://www.yaozhuasocial.com/yaozhua_shequ/#/pages/authorInfo/authorInfo?authorId=${uni.getStorageSync(
              "userId",
            )}&token=${uni.getStorageSync("token")}`,
          });
          break;
        default:
          console.log("运行在开发者工具上");
          break;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
page {
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.f26 {
  font-size: 26rpx;
  font-weight: 800;
}

.f16 {
  font-size: 16rpx;
  font-weight: 500;
}

.numBoxItem {
  margin-right: 48rpx;
  display: flex;
  align-items: center;
}

.bgImg {
  width: 100%;
  height: 392rpx;
  z-index: -1;
}

.bgImg image {
  width: 100%;
  height: 100%;
}

.setImg {
  width: 48rpx;
  height: 48rpx;
  position: absolute;
  top: 34rpx;
  right: 28rpx;
}

.setImg image {
  width: 100%;
  height: 100%;
}

.userBox {
  display: flex;
  align-items: center;
  position: absolute;
  top: 128rpx;
  left: 28rpx;
  z-index: 1;
}

.leftBox {
}

.headImg {
  position: relative;
}

.headImg image {
  width: 156rpx;
  height: 156rpx;
  border-radius: 50%;
}

.addImg {
  position: absolute;
  bottom: 0%;
  left: 114rpx;
}

.addImg image {
  width: 40rpx;
  height: 40rpx;
}

.infoBox {
  margin-left: 32rpx;
  height: 156rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.nameBox text {
  font-weight: 800;
  font-size: 40rpx;
  color: #303030;
  text-align: left;
}

.nameBox image {
  width: 28rpx;
  height: 28rpx;
  margin-left: 16rpx;
}

.IPBox {
  font-weight: 500;
  font-size: 20rpx;
  color: #303030;
}

.numBox {
  color: #303030;
  display: flex;
  align-items: center;
}

.signBox {
  width: 260rpx;
  height: 28rpx;
  position: absolute;
  top: 332rpx;
  left: 28rpx;
}

.signBox image {
  width: 100%;
  height: 100%;
}

.switchTabs {
  margin: 32rpx 28rpx 32rpx;
  display: flex;
  align-items: center;
}

.tabsBox {
  white-space: nowrap;
  background-color: #ffffff;
  font-weight: 400;
  font-size: 28rpx;
  color: #bfbfbf;
  text-align: center;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 48rpx;
  padding: 0 28rpx;
  margin-right: 16rpx;
}

.actTabsBox {
  white-space: nowrap;
  background-color: #d9f700;
  font-weight: bold;
  font-size: 28rpx;
  color: #303030;
  text-align: center;
  height: 48rpx;
  line-height: 48rpx;
  border-radius: 48rpx;
  margin-right: 16rpx;
  padding: 0 90rpx;
}
.topUserInfo {
  position: relative;
}

.topUserInfo::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.9) 0%,
    rgba(0, 0, 0, 0.7) 25%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.5) 70%,
    rgba(0, 0, 0, 0.4) 100%
  );
  pointer-events: none;
}
</style>
